<template>
    <div class="layout" :class="{ 'mobile-menu': isMobileView, 'menu-visible': isMenuVisible }">
        <!-- Website Information Section (1/3 of the layout) -->
        <div class="website-info">
            <el-image :src="config.systemLogo" v-if="config.systemLogo" style="width: 40px; height: 40px;margin-right: 8px;"
                class="website-logo" />
            <chatgptWhite class="website-logo" v-else style="width: 40px; height: 40px;" />
            <span class="website-name">{{ config.systemName }}</span>
        </div>

        <!-- Menu Section -->
        <div class="layout-mid">
            <el-menu :router="true" :default-active="$route.path" class="custom-menu">
                <el-menu-item index="/carPage" @click="handleMenuClick">
                    <div class="menu-item-content">
                        <div class="menu-item-left">
                            <chatgptIcon class="icon" />
                            <span>{{ $t('menu.chatgpt') }}</span>
                        </div>
                        <span v-if="rateLimit" class="rate-limit">{{ rateLimit }}</span>
                    </div>
                </el-menu-item>
                <el-menu-item index="/claudeCarPage" v-if="enableClaude === 'true'" @click="handleMenuClick">
                    <claudeIcon class="icon" />
                    <span>{{ $t('menu.claude') }}</span>
                </el-menu-item>
                <el-menu-item index="/grokCarPage" v-if="enableGrok === 'true'" @click="handleMenuClick">
                    <grokIcon class="icon" />
                    <span>{{ $t('menu.grok') }}</span>
                </el-menu-item>
                <el-menu-item index="/paint" v-if="enableDraw === 'true'" @click="handleMenuClick">
                    <drawSvg class="icon" />
                    <span>{{ $t('menu.drawing') }}</span>
                </el-menu-item>
                <el-divider class="menu-divider" />
                <el-menu-item index="/purchase" @click="handleMenuClick" v-if="enableBuy === 'true'">
                    <purchaseIcon class="icon" />
                    <span>{{ $t('menu.membership') }}</span>
                </el-menu-item>
                <el-menu-item @click="openExchangeDialog">
                    <exchangeIcon class="icon" />
                    <span>{{ $t('menu.exchange') }}</span>
                </el-menu-item>
                <el-menu-item @click="selectOption('useNote')" v-if="enableUseNote === 'true'">
                    <noteIcon class="icon" />
                    <span>{{ $t('menu.useNote') }}</span>
                </el-menu-item>
                <el-menu-item @click="selectOption('invite')" v-if="enableInvite === 'true'">
                    <inviteIcon class="icon" />
                    <span>{{ $t('menu.invite') }}</span>
                </el-menu-item>
                <el-menu-item @click="tryToShowNotification">
                    <noticeIcon class="icon" />
                    <span>{{ $t('menu.notification') }}</span>
                </el-menu-item>
            </el-menu>
        </div>

        <!-- Spacer to push user-info to bottom -->
        <div class="flex-spacer"></div>

        <!-- User Information Section -->
        <div class="user-info" :class="{ 'not-logged-in': !isLogined, 'show-dropdown': dropDownVisible }" @click="gotoLogin">
            <el-avatar :size="40" :src="user.avatar || avatar" v-if="isLogined" />
            <el-avatar :size="40" :src="avatar" v-else class="clickable-avatar" />
            <div class="name-section" v-if="isLogined">
                <div class="user-name">
                    {{ user.userToken }}
                </div>
                <div class="expiry-section">
                    {{ formatExpiryDate }}过期
                </div>
            </div>
            <div class="name-section" v-else>
                <div class="login-prompt">{{ $t('menu.clickToLogin') || '点击头像登录' }}</div>
            </div>
            <el-dropdown @command="handleCommand" trigger="click" :offset="20" placement="top" v-if="isLogined"
                :popper-options="{
                    'modifiers': [{
                        'name': 'offset',
                        'options': {
                            'offset': [-87, 12],
                        }
                    }]
                }" :teleported="true" :popper-class="['user-dropdown-menu']"
                @visible-change="userDropDownVisibleCallBack">
                <div class="arrow-icon">
                    <el-icon>
                        <ArrowUp />
                    </el-icon>
                </div>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item command="userCenter">
                            <el-icon>
                                <userIcon />
                            </el-icon>
                            {{ $t('menu.userCenter') }}
                        </el-dropdown-item>
                        <!-- <el-dropdown-item command="myWorks">
                            <el-icon>
                                <drawIcon />
                            </el-icon>
                            {{ $t('menu.myWorks') }}
                        </el-dropdown-item> -->
                        <el-dropdown-item command="changePassword">
                            <el-icon>
                                <Lock />
                            </el-icon>
                            {{ $t('menu.changePassword') }}
                        </el-dropdown-item>
                        <el-dropdown-item command="logout" divided>
                            <el-icon>
                                <logoutIcon class="logout-icon" />
                            </el-icon>
                            {{ $t('menu.logout') }}
                        </el-dropdown-item>
                        <el-dropdown-item command="gotoBackend" v-if="user.userToken == 'admin'" divided>
                            <el-icon>
                                <panelIcon />
                            </el-icon>
                            {{ $t('menu.backend') }}
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div>
    </div>
    <!-- 移动端菜单按钮现在由HeaderActions提供 -->
    <!-- 移动端遮罩层 -->
    <div class="mobile-overlay" v-if="isMobileView && isMenuVisible" @click="closeMobileMenu"></div>
    <el-dialog v-model="showNotification" title="站内通知">
        <div v-richText="notification.content">

        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" @click="handleRead">
                    我知道了
                </el-button>
            </div>
        </template>
    </el-dialog>
    <!-- Password Change Dialog -->
    <ChangePasswordDialog v-model="passwordDialogVisible" :user="user" @success="onPasswordChangeSuccess" />
    <UserCenter ref="userCenterRef" />

    <!-- 站内兑换悬浮窗 -->
    <ExchangeDialog v-model:visible="exchangeDialogVisible" @success="exchangeDialogVisible = false" />
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { ref, defineProps, onMounted, getCurrentInstance, computed, reactive, nextTick, defineExpose } from 'vue'
import { House, Document, Setting, User, ShoppingCart, Present, Notification, Lock, SwitchButton, ArrowUp, Key } from '@element-plus/icons-vue'
import { useRouter, useRoute } from 'vue-router';
import chatgptIcon from '@/assets/chatgpt.svg'
import chatgptWhite from '@/assets/chatgpt-white.svg'
import claudeIcon from '@/assets/claude.svg'
import grokIcon from '@/assets/grok.svg'
import exchangeIcon from '@/assets/exchange.svg'
import drawSvg from '@/assets/draw.svg'
import drawIcon from '@/assets/draw.svg'
import purchaseIcon from '@/assets/purchase.svg'
import noteIcon from '@/assets/note.svg'
import inviteIcon from '@/assets/invite.svg'
import noticeIcon from '@/assets/notice.svg'
import foxIcon from '@/assets/fox.svg'
import userIcon from '@/assets/dropdown/user.svg'
import logoutIcon from '@/assets/dropdown/logout.svg'
import panelIcon from '@/assets/dropdown/panel.svg'
import api from '@/axios'
import { isMobile } from '@/utils'
import { useNotificationStore } from '../store/notificationStore'
import { useDark } from '@vueuse/core'
import useUserStore from '@/store/user'
import moment from 'moment'
import avatar from '@/assets/kid.png'
import { ElMessage, ElMessageBox } from 'element-plus'
import UserCenter from '@/components/draw/UserCenter.vue'
import { useWindowSize } from '@vueuse/core'
import ExchangeDialog from '@/components/ExchangeDialog.vue'
import ChangePasswordDialog from '@/components/ChangePasswordDialog.vue'
const { t } = useI18n()
const props = defineProps({
    config: {
        type: Object,
        required: true,
    },
})
const enableDraw = computed(() => props.config.enableDraw)
const enableClaude = computed(() => props.config.enableClaude)
const enableGrok = computed(() => props.config.enableGrok)
const enableFreeNode = computed(() => props.config.enableFreeNode)
const enableUseNote = computed(() => props.config.enableUseNote)
const enableBuy = computed(() => props.config.enableBuy)
const enableInvite = computed(() => props.config.enablePromotion)
const notificationStore = useNotificationStore()
const router = useRouter()

const route = useRoute()
const dropDownVisible = ref(false)
const userDropDownVisibleCallBack = (visible) => {
    dropDownVisible.value = visible
}

const active_menu = ref('')
const isDark = useDark()
const customToggleDark = (e) => {
    console.log(e);
    if (e) {
        //暗色主题
        const theme = 'dark'
        document.documentElement.setAttribute('class', theme)
    } else {
        //亮色主题
        const theme = 'light'
        document.documentElement.setAttribute('class', theme)
    }
};


const noteSite = props['config']['noteSite']
const showOptions = ref(false);

const toggleOptions = () => {
    showOptions.value = !showOptions.value;
};

const selectOption = (option) => {
    if (option === 'theme') {
        customToggleDark(isDark)
    } else if (option === 'purchase') {
        router.push('/purchase')
    } else if (option === 'chatgpt') {
        router.push('/carPage')
    } else if (option === 'claude') {
        router.push('/claudeCarPage')
    } else if (option === 'useNote') {
        if (noteSite) {
            let url = noteSite
            if (!/^https?:\/\//i.test(url)) {
                url = 'https://' + url;
            }
            window.open(url, '_blank')
            if (isMobileView.value) {
                closeMobileMenu()
            }
            return
        } else {
            router.push('/useNote')
            if (isMobileView.value) {
                closeMobileMenu()
            }
        }
    } else if (option === 'announcement') {
        getHeaderNotification()
    } else if (option === 'exchange') {
        openExchangeDialog()
    } else if (option === 'invite') {
        router.push('/invite')
    }
    showOptions.value = false; // Hide options after selection
};
const { proxy } = getCurrentInstance()
const showNotification = ref(false)
const notification = ref('')
const getHeaderNotification = async () => {
    const res = await api.get('/api/notification/getLatest?typeList=2')
    if (res.status == 200 && res.data.data) {
        let notifications = res.data.data
        if (notifications.length) {
            //根据createdAt 排序，最新的放在前面
            let theLatest = proxy.$getLatestNotification(notifications)
            if (theLatest) {
                notification.value = theLatest
                if (notificationStore.isVisible(theLatest.updatedAt)) {
                    showNotification.value = true
                }
            }
        }
    }
}
const handleRead = () => {
    showNotification.value = false
    notificationStore.markAsRead('headerNotification', notification.value.updatedAt)
}

const tryToShowNotification = () => {
    if (notification.value) {
        showNotification.value = true
    } else {
        ElMessage.info('暂无通知')
    }
}

// User related data
const userStore = useUserStore()
const user = ref(userStore.user)
const isLogined = ref(userStore.isLoggedIn)
const formatExpiryDate = ref('')
const userTypeTip = ref('')
const passwordDialogVisible = ref(false)
const rateLimits = ref([])

const gpt4oRateLimit = computed(() => {
    return rateLimits.value.find(item =>
        item.model && (item.model.toLowerCase() === 'gpt-4o' || item.model.toLowerCase() === 'gpt4o' || item.model.toLowerCase() === 'gpt-4o-mini')
    )
})

const rateLimit = computed(() => {
    const limit = gpt4oRateLimit.value || rateLimits.value[0]
    if (!limit) return ''
    const periodMap = {
        '1h': '小时',
        '3h': '3小时',
        '1d': '天',
        '1w': '周'
    }
    const period = periodMap[limit.per] || limit.period
    return `${limit.limit}次/${period}`
})

// User actions
const logout = () => {
    ElMessageBox.confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        userStore.logout()
        ElMessage.info('已退出登录')
        isLogined.value = false
    })
}

const handleCommand = (command) => {
    switch (command) {
        case 'changePassword':
            passwordDialogVisible.value = true
            break
        case 'logout':
            logout()
            break
        case 'gotoBackend':
            gotoBackend()
            break
        case 'myWorks':
            gotoMyWorks()
            break
        case 'userCenter':
            gotoUserCenter()
            break
    }
}

const gotoLogin = () => {
    if(isLogined.value){
        return
    }
    ElMessageBox.confirm('确定要前往登录页面吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        router.push('/login')
    })
}

const gotoMyWorks = () => {
    router.push('/myWorks')
}

const userCenterRef = ref(null)
const gotoUserCenter = () => {
    userCenterRef.value.openDialog()
}

// Initialize user data
if (isLogined.value) {
    const now = moment()
    const plusExpireTime = moment(user.value.plusExpireTime)
    const expireTime = moment(user.value.expireTime)
    if (plusExpireTime.isAfter(now)) {
        formatExpiryDate.value = plusExpireTime.format('YY.MM.DD')
    } else if (expireTime.isAfter(now)) {
        formatExpiryDate.value = expireTime.format('YY.MM.DD')
    } else {
        formatExpiryDate.value = '免费用户'
    }
    const userType = user.value.userType
    if (userType == 2) {
        userTypeTip.value = '尊贵的会员用户'
    } else if (userType == 3) {
        userTypeTip.value = '尊贵的高级会员用户'
    }
}

onMounted(async () => {
    if (route.path == '/home') {

        active_menu.value = '/carPage'
        router.push('/carPage')
    }

    notificationStore.initReadStatus('headerNotification')
    getHeaderNotification()


    // 拉取限速信息
    if (isLogined.value) {
        try {
            const res = await api.get('/api/chatGptUser/getRateLimit')
            if (res.status === 200 && res.data.code === 0) {
                rateLimits.value = res.data.data || []
            }
        } catch (e) {
            // 忽略错误
        }
    }
})

const exchangeDialogVisible = ref(false)

const openExchangeDialog = () => {
    exchangeDialogVisible.value = true
}

const gotoBackend = () => {
    router.push('/dashboard/today')
}

// Add new refs and computed properties
const { width } = useWindowSize()
const isMobileView = computed(() => width.value <= 768)
const isMenuVisible = ref(false)

// Add new methods
const toggleMobileMenu = () => {
    isMenuVisible.value = !isMenuVisible.value
    if (isMenuVisible.value) {
        document.body.style.overflow = 'hidden'
    } else {
        document.body.style.overflow = ''
    }
}

const closeMobileMenu = () => {
    isMenuVisible.value = false
    document.body.style.overflow = ''
}

const handleMenuClick = () => {
    if (isMobileView.value) {
        closeMobileMenu()
    }
}

// 暴露方法给父组件调用
defineExpose({
    toggleMobileMenu,
    closeMobileMenu
})

function onPasswordChangeSuccess() {
  // Optionally handle additional logic after password change, e.g., show a message or refresh user info
}
</script>

<style scoped>
.layout {
    display: flex;
    flex-direction: column;
    gap: 10px;
    height: 100vh;
    /* Make layout take full height */
    padding-right: 10px;
}

.website-info {
    display: flex;
    padding: 16px;
    padding-left: 40px;
    padding-right: 20px;
    align-items: center;
    /* gap: 12px; */
    align-self: stretch;
    justify-content: start;
}

.website-logo {
    border-radius: 50%;
}

.website-name {
    color: #FFF;
    font-family: MiSans;
    font-size: 22px;
    font-style: normal;
    font-weight: 520;
    line-height: normal;
    letter-spacing: -1.1px;
}

.layout-mid {
    flex: 2;
    text-align: center;
    padding: 20px;
    padding-top: 0px;
    padding-right: 0px;
    border-radius: 15px;
    /* box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); */
}

.menu-divider {
    margin: 0px;
    stroke-width: 1px;
    stroke: rgba(255, 255, 255, 0.10);
    border-top: 1px solid rgba(255, 255, 255, 0.20);
}

.custom-menu {
    border-radius: 8px;
    border: none;
    padding: 4px;
    /* box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); */
    color: var(--el-text-color-regular);
}

.custom-menu .el-menu-item span {
    color: #FFF;
    font-family: MiSans;
    font-size: 15px;
    font-style: normal;
    font-weight: 520;
    line-height: normal;
}

/* Make sure menu text is white except rate limit */
.custom-menu .el-menu-item span:not(.rate-limit) {
    color: #FFF;
}

/* More specific selector for rate-limit */
.custom-menu .el-menu-item .rate-limit {
    margin-left: 12px;
    font-size: 13px;
    color: #FFF;
    opacity: 0.6;
    padding: 4px 8px;
    border-radius: 8px;
    white-space: nowrap;
}

.el-menu {
    background-color: transparent;
    --el-menu-base-level-padding: 16px;
}

.custom-menu .el-menu-item,
.custom-menu .el-sub-menu__title {
    display: flex;
    padding: 16px;
    align-items: center;
    gap: 6px;
    align-self: stretch;
    border-radius: 16px;
}

.custom-menu .el-menu-item.is-active {
    border-radius: 16px;
    border: 1px solid rgba(0, 0, 0, 0.10);
    background: #FFF;
}

.custom-menu .el-menu-item.is-active span {
    color: #000 !important;
}

.custom-menu .el-menu-item:hover {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    /* border: 1px solid rgba(0, 0, 0, 0.10); */
}

.custom-menu .el-menu-item:hover span {
    color: #000 !important;
}



/* 子菜单样式 */
.el-menu--popup {
    border-radius: 6px;
    padding: 6px;
    min-width: 150px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.el-menu--popup .el-menu-item {
    height: 48px;
    line-height: 48px;
    padding: 0 20px;
    border-radius: 4px;
}

/* 动画效果 */
.el-menu-item,
.el-sub-menu__title {
    position: relative;
    overflow: hidden;
}

.el-menu-item::after,
.el-sub-menu__title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    transition: all 0.3s ease;
}

.el-menu-item:hover::after,
.el-sub-menu__title:hover::after {
    width: 80%;
    left: 10%;
}

@keyframes menuFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.el-menu--popup {
    animation: menuFadeIn 0.2s ease-out;
}


.icon {
    margin-right: 8px;
    vertical-align: middle;
    font-size: 18px;
    color: var(--el-text-color-regular);
}

.flex-spacer {
    flex-grow: 1;
}

.user-info {
    margin-top: auto;
    display: flex;
    padding: 16px;
    padding-top: 5px;
    padding-bottom: 5px;
    align-items: center;
    gap: 12px;
    align-self: stretch;
    border-radius: 16px;
    border: 1px solid rgba(0, 0, 0, 0.10);
    background: rgba(0, 0, 0, 0.05);
    justify-content: space-between;
    margin-left: 20px;
    margin-bottom: 20px;
}

.name-section{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    flex: 1 0 0;
}

.user-name {
    color: #000;
    font-family: MiSans;
    font-size: 16px;
    font-style: normal;
    font-weight: 450;
    line-height: normal;
}

.expiry-section {
    color: #000;
    font-family: MiSans;
    font-size: 12px;
    font-style: normal;
    font-weight: 450;
    line-height: normal;
    opacity: 0.3;
}




.arrow-icon {
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.arrow-icon:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.arrow-icon .el-icon {
    font-size: 16px;
    color: var(--el-text-color-regular);
}



/* Mobile Styles */
@media (max-width: 768px) {
    .layout {
        position: fixed;
        top: 0;
        left: -100%;
        width: 65%;
        max-width: 250px;
        height: 100vh;
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.00) 43.99%, rgba(255, 255, 255, 0.14) 55%, #FFF 100%), radial-gradient(232.08% 120.13% at 68.41% 100%, #E9EEF1 0%, #44BDFF 100%);
        z-index: 2000;
        transition: left 0.3s ease;
        display: flex;
        padding: 20px 10px;
        overflow-y: auto;
    }

    .layout.mobile-menu.menu-visible {
        left: 0;
    }


    .mobile-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1999;
        backdrop-filter: blur(4px);
    }

    .website-info {
        padding: 12px 8px;
        padding-left: 16px; /* 减少左边距 */
    }
    
    .website-name {
        font-size: 18px; /* 稍微减小字体 */
    }

    .layout-mid {
        padding: 8px; /* 减少内边距 */
        flex: 1; /* 让菜单区域占据更多空间 */
    }

    .custom-menu {
        width: 100%;
    }

    .custom-menu .el-menu-item {
        padding: 10px 8px; /* 减少内边距 */
    }

    .user-info {
        display: none; /* 隐藏用户信息部分 */
    }

}

/* 优化移动端动画效果 */
@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }

    to {
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.mobile-menu.menu-visible {
    animation: slideIn 0.3s ease forwards;
}

.mobile-overlay {
    animation: fadeIn 0.3s ease forwards;
}

/* 优化移动端触摸体验 */
@media (hover: none) {
    .custom-menu .el-menu-item:active {
        background: rgba(255, 255, 255, 0.9);
    }

}

.user-info.not-logged-in {
    justify-content: flex-start;
    gap: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.user-info.show-dropdown {
    background: #FFFFFF;
    border-radius: 0px 0px 16px 16px;
    border: none
}

.user-info.not-logged-in:hover {
    background: rgba(0, 0, 0, 0.1);
}

.clickable-avatar {
    cursor: pointer;
    transition: transform 0.2s ease;
}

.clickable-avatar:hover {
    transform: scale(1.05);
}

.login-prompt {
    color: #000;
    font-family: MiSans;
    font-size: 14px;
    font-style: normal;
    font-weight: 450;
    line-height: normal;
    opacity: 0.6;
}

@media (max-width: 768px) {
    .user-info.not-logged-in {
        padding: 12px;
        margin: 10px;
    }

    .login-prompt {
        font-size: 13px;
    }
}

/* Dropdown Styles */
:deep(.el-dropdown) {
    display: flex;
    align-items: center;
}

:global(.el-popper.user-dropdown-menu) {
    width: 230px;
}

/* 自定义下拉菜单的样式 */
:global(.el-popper.is-light.user-dropdown-menu) {
    border: none;
    border-radius: 12px;
    box-shadow: none;
}

:global(.el-popper.user-dropdown-menu .el-dropdown-menu) {
    padding: 8px;
    border-radius: 16px 16px 0px 0px;
    background: #FFFFFF;
}

:global(.el-popper.user-dropdown-menu .el-dropdown-menu__item) {
    display: flex;
    padding: 12px 16px;
    align-items: center;
    gap: 10px;
    align-self: stretch;
    /* justify-content: center; */
    color: #000;
    font-family: MiSans;
    font-size: 14px;
    font-style: normal;
    font-weight: 380;
    line-height: normal;
}

/* Logout menu item specific styles */
:global(.el-popper.user-dropdown-menu .el-dropdown-menu__item:has(svg.logout-icon)) {
    stroke: #E73535;
    color: #E73535;
    font-family: MiSans;
    font-size: 14px;
    font-style: normal;
    font-weight: 380;
    line-height: normal;
}

:global(.el-popper.user-dropdown-menu .el-dropdown-menu__item:has(svg.logout-icon) svg) {
    color: #E73535;
}

:global(.el-popper.user-dropdown-menu .el-dropdown-menu__item:hover) {
    background-color: #f5f7fa;
    color: #409EFF;
}

:deep(.el-dropdown-menu__item .el-icon) {
    margin-right: 8px;
    font-size: 16px;
    vertical-align: middle;
}

:deep(.el-dropdown-menu__item--divided) {
    margin-top: 8px;
    border-top: 1px solid #ebeef5;
}

:deep(.el-dropdown-menu__item--divided:before) {
    height: 0px;
    margin: 0;
}

/* 调整下拉菜单的位置 */
:deep(.el-popper[data-popper-placement^='top']) {
    margin-bottom: 8px;
}

:deep(.el-popper[data-popper-placement^='bottom']) {
    margin-top: 8px;
}

/* 自定义箭头样式 */
:global(.el-popper.user-dropdown-menu .el-popper__arrow) {
    display: none;
}

@media (max-width: 768px) {
    :deep(.el-dropdown-menu) {
        width: 200px;
    }

    :deep(.el-dropdown-menu__item) {
        padding: 10px 12px;
        font-size: 13px;
    }
}



.menu-item-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.menu-item-left {
    display: flex;
    align-items: center;
    gap: 6px;
}

.custom-menu .el-menu-item {
    padding: 16px;
}

.rate-limit {
    margin-left: 12px;
    font-size: 13px;
    color: #000;
    opacity: 0.6;
    padding: 4px 8px;
    border-radius: 8px;
    white-space: nowrap;
}

</style>