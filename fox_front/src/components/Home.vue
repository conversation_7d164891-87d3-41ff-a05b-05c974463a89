<!-- App.vue -->
<template>
  <el-container class="app-container">
    <!-- 侧边栏 -->
    <el-aside>
      <!-- <UserInfo v-if="!isAsideCollapsed" :config="config" /> -->
      <LeftMenu ref="leftMenuRef" :config="config" />
    </el-aside>

    <el-container class="right-container">
      <!-- 移动端头部 -->
      <el-header class="mobile-header" v-if="isMobileView">
        <HeaderActions :config="config" @toggle-mobile-menu="toggleMobileMenu" />
      </el-header>
      <!-- <el-header>
        <HeaderActions :config="config" />
      </el-header> -->
      <el-main>
        <el-backtop target=".el-main" :bottom="100" :right="100">
        </el-backtop>
        <!-- <NodeTabs :config="config" />
          -->
        <router-view :config="config" />
      </el-main>
    </el-container>

  </el-container>
</template>

<script>
import { ref, computed, reactive, onMounted, onBeforeUnmount, watch } from 'vue'
import UserInfo from './UserInfo.vue'
import HeaderActions from './HeaderActions.vue'
import NodeTabs from './NodeTabs.vue'
import useUserStore from '../store/user'
import LeftMenu from './LeftMenu.vue'
import api from '@/axios'
import { useRouter } from 'vue-router'
import { useScriptLoader } from '@/useScriptLoader'
import { useWindowSize } from '@vueuse/core'

export default {
  name: 'App',
  components: {
    UserInfo,
    LeftMenu,
    HeaderActions,
    NodeTabs
  },
  setup() {
    const router = useRouter()
    const userStore = useUserStore()
    const user = ref(userStore.user)
    // const isLogined = ref(userStore.isLoggedIn)
    const isLogined = userStore.isLoggedIn
    const config = ref({})
    // 控制侧边栏的展开/收起
    const isAsideCollapsed = ref(false)
    
    // 移动端适配
    const { width } = useWindowSize()
    const isMobileView = computed(() => width.value <= 768)
    
    // 左侧菜单引用
    const leftMenuRef = ref(null)
    
    // 切换移动端菜单
    const toggleMobileMenu = () => {
      if (leftMenuRef.value) {
        leftMenuRef.value.toggleMobileMenu()
      }
    }

    const getConfig = async () => {
      const res = await api.post('/api/config/get',
        ["systemName", "systemLogo", "siteNotice", "noteSite", "issuingCardSite", "claudeUrl",
          "nodeFreeName", "node4oName", "nodePlusName", "nodeClaudeName", "nodeGrokName", 'scripts', 'enableVisitor', 'enableDraw',
          "enableClaude", "enableGrok", "lyyClaudeUrl", "grokUrl", "enableUserTokenLogin", "enableFreeNode", "enableUseNote",
          "enable4oPlus", "enableBuy", "enablePromotion"
        ])
      if (res.status !== 200) {
        ElMessage.error('获取配置失败')
        return
      }
      config.value = res.data.data
      console.log(config.value)
      
      // 配置更新后立即更新favicon和title
      if (config.value.systemLogo) {
        updateFavicon(config.value.systemLogo)
      }
      if (config.value.systemName) {
        updateTitle(config.value.systemName)
      }
    }
    
    // 动态更新favicon的函数
    const updateFavicon = (logoUrl) => {
      // 移除现有的favicon
      const existingFavicon = document.querySelector('link[rel="icon"]')
      if (existingFavicon) {
        existingFavicon.remove()
      }
      
      // 创建新的favicon link元素
      const favicon = document.createElement('link')
      favicon.rel = 'icon'
      
      if (logoUrl) {
        // 如果有自定义logo，使用自定义logo
        favicon.href = logoUrl
        // 根据文件扩展名判断类型
        if (logoUrl.toLowerCase().includes('.svg')) {
          favicon.type = 'image/svg+xml'
        } else if (logoUrl.toLowerCase().includes('.png')) {
          favicon.type = 'image/png'
        } else if (logoUrl.toLowerCase().includes('.ico')) {
          favicon.type = 'image/x-icon'
        }
        
        // 添加错误处理，如果logo加载失败，回退到默认图标
        favicon.onerror = () => {
          console.warn('自定义favicon加载失败，使用默认图标')
          favicon.href = '/fox.svg'
          favicon.type = 'image/svg+xml'
        }
      } else {
        // 如果没有自定义logo，使用默认的fox.svg
        favicon.href = '/fox.svg'
        favicon.type = 'image/svg+xml'
      }
      
      // 添加到head中
      document.head.appendChild(favicon)
    }

    // 动态更新页面标题的函数
    const updateTitle = (systemName) => {
      if (systemName) {
        document.title = systemName
      } else {
        document.title = 'Fox'  // 默认标题
      }
    }

    // 监听systemLogo的变化，自动更新favicon
    watch(
      () => config.value.systemLogo,
      (newLogo) => {
        updateFavicon(newLogo)
      }
    )

    // 监听systemName的变化，自动更新页面标题
    watch(
      () => config.value.systemName,
      (newName) => {
        updateTitle(newName)
      }
    )

    onMounted(async () => {
      await getConfig()
      
      if (config.value.scripts) {
        try {
          const { loadScriptTags } = useScriptLoader()
          await loadScriptTags(config.value.scripts)
        } catch (error) {
          console.error(error)
        }
      }
    })

    return {
      user,
      isLogined,
      isAsideCollapsed,
      config,
      isMobileView,
      leftMenuRef,
      toggleMobileMenu,
    }
  }
}
</script>


<style scoped>
.app-container {
  height: 100vh;
  width: 100%;
  max-width: 100vw;
  overflow-y: auto;
  overflow-x: hidden;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.00) 43.99%, rgba(255, 255, 255, 0.14) 55%, #FFF 100%), radial-gradient(232.08% 120.13% at 68.41% 100%, #E9EEF1 0%, #44BDFF 100%);
  box-sizing: border-box;
  /* font-family: 'Arial, sans-serif'; */

}

.right-container {}

.el-aside {
  position: relative;
  width: inherit;
  width: 260px;
}

.collapse-btn {
  position: absolute;
  top: 10px;
  right: -20px;
  cursor: pointer;
  padding: 5px;
  border: 1px solid var(--el-border-color);
  border-radius: 50%;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
}

.el-header {
  border-radius: 10px;
  height: auto;
  margin-bottom: 10px;
}

/* 移动端头部样式 */
.mobile-header {
  height: auto !important;
  padding: 0 !important;
  margin: 0 !important;
  border-radius: 0 !important;
  background: transparent !important;
  border-bottom: none !important;
}

.el-main {
  padding: 20px;
  /* padding-top: 0px; */
  /* margin-top: 10px; */
  /* height: 100%; */
  overflow: auto;
  overflow-x: hidden;
  border-radius: 10px;
  border-top: 1px solid var(--el-border-color);
  border-radius: 16px;
background: #FFF;
margin: 20px;
}

.layout {
  display: flex;
  flex-direction: column;
  gap: 10px;
  /* padding: 10px; */
  padding-right: 10px;
}

.website-info {
  flex: 1;
  text-align: center;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.website-logo {
  margin-bottom: 10px;
  border-radius: 50%;
}

.website-name {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 10px;
  /* margin-left: 20px; */
}

.user-info {
  flex: 2;
  text-align: center;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {

  .el-aside {
    width: 0px;
  }
  
  .right-container {
    flex-direction: column;
    width: 100%;
    max-width: 100vw;
    box-sizing: border-box;
  }
  
  .el-main {
    margin: 0;
    margin-top: 60px; /* 调整为头部高度：60px(仅头部) */
    border-radius: 0;
    padding: 16px;
    padding-left: 6px;
    padding-right: 6px;
    width: 100%;
    max-width: 100vw;
    box-sizing: border-box;
    overflow-x: hidden;
  }
  
  .mobile-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1300; /* 确保高于下拉菜单的z-index(1200) */
    width: 100%;
    max-width: 100vw;
    box-sizing: border-box;
  }
}
</style>