<template>
    <div class="pagination-container">
        <!-- 工具栏部分 -->
        <el-row :gutter="10" class="toolbar">
            <el-col :xs="24" :sm="6" :md="4" :lg="3" class="toolbar-item">
                <el-input v-model="searchQuery.query" placeholder="请输入用户标识"></el-input>
            </el-col>
            <el-col :xs="12" :sm="4" :md="3" :lg="2" class="toolbar-item">
                <el-select v-model="searchQuery.status" placeholder="状态筛选" clearable>
                    <el-option label="全部" value="" />
                    <el-option label="待处理" :value="0" />
                    <el-option label="成功" :value="1" />
                    <el-option label="失败" :value="2" />
                </el-select>
            </el-col>
            <el-col :xs="12" :sm="4" :md="3" :lg="2" class="toolbar-item">
                <el-button type="primary" @click="fetchData" :icon="Search">查询</el-button>
            </el-col>
            <el-col :xs="12" :sm="4" :md="3" :lg="2" class="toolbar-item">
                <el-button @click="resetSearch" :icon="Refresh">重置</el-button>
            </el-col>
        </el-row>

        <!-- 统计信息 -->
        <el-row :gutter="20" class="stats-row">
            <el-col :xs="12" :sm="6" :md="6" :lg="6">
                <el-card class="stats-card">
                    <div class="stats-content">
                        <div class="stats-value">{{ stats.totalCount }}</div>
                        <div class="stats-label">总申请数</div>
                    </div>
                </el-card>
            </el-col>
            <el-col :xs="12" :sm="6" :md="6" :lg="6">
                <el-card class="stats-card pending">
                    <div class="stats-content">
                        <div class="stats-value">{{ stats.pendingCount }}</div>
                        <div class="stats-label">待处理</div>
                    </div>
                </el-card>
            </el-col>
            <el-col :xs="12" :sm="6" :md="6" :lg="6">
                <el-card class="stats-card success">
                    <div class="stats-content">
                        <div class="stats-value">{{ stats.successCount }}</div>
                        <div class="stats-label">已成功</div>
                    </div>
                </el-card>
            </el-col>
            <el-col :xs="12" :sm="6" :md="6" :lg="6">
                <el-card class="stats-card failed">
                    <div class="stats-content">
                        <div class="stats-value">{{ stats.failedCount }}</div>
                        <div class="stats-label">已失败</div>
                    </div>
                </el-card>
            </el-col>
        </el-row>

        <!-- 桌面端表格 -->
        <el-table v-if="!isMobile()" :data="tableData" style="width: 100%" v-loading="loading">
            <el-table-column prop="id" label="ID" min-width="80" />
            <el-table-column prop="userToken" label="用户标识" min-width="120">
                <template #default="scope">
                    <el-text class="user-token">{{ scope.row.userToken }}</el-text>
                </template>
            </el-table-column>
            <el-table-column prop="amount" label="提现金额" min-width="100" sortable>
                <template #default="scope">
                    <el-text type="primary" class="amount-text">¥{{ (scope.row.amount || 0).toFixed(2) }}</el-text>
                </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" min-width="90">
                <template #default="scope">
                    <el-tag :type="getStatusType(scope.row.status)" size="small">
                        {{ getStatusText(scope.row.status) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="withdrawTime" label="申请时间" min-width="160">
                <template #default="scope">
                    <el-text size="small">{{ formatDateTime(scope.row.withdrawTime) }}</el-text>
                </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip />
            <el-table-column label="操作" min-width="200" fixed="right">
                <template #default="scope">
                    <el-button type="info" size="small" @click="viewDetails(scope.row)">
                        详情
                    </el-button>
                    <el-button 
                        v-if="scope.row.status === 0" 
                        type="success" 
                        size="small" 
                        @click="processWithdraw(scope.row, 1)">
                        通过
                    </el-button>
                    <el-button 
                        v-if="scope.row.status === 0" 
                        type="danger" 
                        size="small" 
                        @click="processWithdraw(scope.row, 2)">
                        拒绝
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 移动端卡片列表 -->
        <div v-else class="mobile-card-list">
            <el-card v-for="item in tableData" :key="item.id" class="mobile-card" shadow="hover">
                <div class="mobile-card-header">
                    <span class="mobile-card-title">ID: {{ item.id }}</span>
                    <el-tag :type="getStatusType(item.status)" size="small">
                        {{ getStatusText(item.status) }}
                    </el-tag>
                </div>
                <div class="mobile-card-content">
                    <div class="mobile-card-item">
                        <span class="label">用户:</span>
                        <span class="value user-token">{{ item.userToken }}</span>
                    </div>
                    <div class="mobile-card-item">
                        <span class="label">金额:</span>
                        <span class="value amount-text">¥{{ (item.amount || 0).toFixed(2) }}</span>
                    </div>
                    <div class="mobile-card-item">
                        <span class="label">申请时间:</span>
                        <span class="value">{{ formatDateTime(item.withdrawTime) }}</span>
                    </div>
                    <div class="mobile-card-item" v-if="item.remark">
                        <span class="label">备注:</span>
                        <span class="value">{{ item.remark }}</span>
                    </div>
                </div>
                <div class="mobile-card-actions">
                    <el-button type="info" size="small" @click="viewDetails(item)">详情</el-button>
                    <el-button 
                        v-if="item.status === 0" 
                        type="success" 
                        size="small" 
                        @click="processWithdraw(item, 1)">
                        通过
                    </el-button>
                    <el-button 
                        v-if="item.status === 0" 
                        type="danger" 
                        size="small" 
                        @click="processWithdraw(item, 2)">
                        拒绝
                    </el-button>
                </div>
            </el-card>
        </div>

        <!-- 分页组件 -->
        <el-pagination
            v-model:current-page="searchQuery.pageNum"
            v-model:page-size="searchQuery.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :small="isMobile()"
            :disabled="loading"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            class="pagination"
        />

        <!-- 详情对话框 -->
        <el-dialog v-model="detailDialogVisible" title="提现申请详情" width="600px" :close-on-click-modal="false">
            <div v-if="currentRecord" class="detail-content">
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="申请ID">{{ currentRecord.id }}</el-descriptions-item>
                    <el-descriptions-item label="用户标识">{{ currentRecord.userToken }}</el-descriptions-item>
                    <el-descriptions-item label="提现金额">
                        <el-text type="primary" class="amount-text">¥{{ (currentRecord.amount || 0).toFixed(2) }}</el-text>
                    </el-descriptions-item>
                    <el-descriptions-item label="状态">
                        <el-tag :type="getStatusType(currentRecord.status)">
                            {{ getStatusText(currentRecord.status) }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="申请时间">{{ formatDateTime(currentRecord.withdrawTime) }}</el-descriptions-item>
                    <el-descriptions-item label="创建时间">{{ formatDateTime(currentRecord.createdAt) }}</el-descriptions-item>
                    <el-descriptions-item label="更新时间">{{ formatDateTime(currentRecord.updatedAt) }}</el-descriptions-item>
                    <el-descriptions-item label="备注" :span="2">
                        <div class="remark-content">{{ currentRecord.remark || '-' }}</div>
                    </el-descriptions-item>
                </el-descriptions>
                
                <!-- 二维码图片 -->
                <div v-if="currentRecord.qrCodeImage" class="qr-code-section">
                    <h4>收款二维码</h4>
                    <el-image 
                        :src="currentRecord.qrCodeImage" 
                        style="width: 200px; height: 200px;"
                        fit="contain"
                        :preview-src-list="[currentRecord.qrCodeImage]"
                        preview-teleported
                    />
                </div>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="detailDialogVisible = false">关闭</el-button>
                    <el-button 
                        v-if="currentRecord && currentRecord.status === 0" 
                        type="success" 
                        @click="processWithdraw(currentRecord, 1)">
                        通过申请
                    </el-button>
                    <el-button 
                        v-if="currentRecord && currentRecord.status === 0" 
                        type="danger" 
                        @click="processWithdraw(currentRecord, 2)">
                        拒绝申请
                    </el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 处理对话框 -->
        <el-dialog v-model="processDialogVisible" :title="processTitle" width="500px" :close-on-click-modal="false">
            <el-form :model="processForm" label-width="80px">
                <el-form-item label="处理结果">
                    <el-tag :type="processForm.status === 1 ? 'success' : 'danger'">
                        {{ processForm.status === 1 ? '通过申请' : '拒绝申请' }}
                    </el-tag>
                </el-form-item>
                <el-form-item label="处理备注">
                    <el-input 
                        v-model="processForm.remark" 
                        type="textarea" 
                        :rows="3"
                        placeholder="请输入处理备注（可选）"
                        maxlength="200"
                        show-word-limit
                    />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="processDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="confirmProcess" :loading="processing">确认处理</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, getCurrentInstance } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import api from '@/axios'
import { isMobile } from '@/utils'

// 响应式数据
const loading = ref(false)
const processing = ref(false)
const tableData = ref([])
const total = ref(0)
const detailDialogVisible = ref(false)
const processDialogVisible = ref(false)
const currentRecord = ref(null)

// 统计数据
const stats = reactive({
    totalCount: 0,
    pendingCount: 0,
    successCount: 0,
    failedCount: 0
})

// 搜索查询参数
const searchQuery = reactive({
    pageNum: 1,
    pageSize: 20,
    query: '',
    status: '',
    sortField: 'withdraw_time',
    sortOrder: 'desc'
})

// 处理表单
const processForm = reactive({
    withdrawRecordId: null,
    status: null,
    remark: ''
})

const { proxy } = getCurrentInstance()


// 处理标题
const processTitle = computed(() => {
    return processForm.status === 1 ? '通过提现申请' : '拒绝提现申请'
})

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        const queryParams = { ...searchQuery }
        // 如果status为空字符串，删除该参数
        if (queryParams.status === '') {
            delete queryParams.status
        }
        
        const res = await api.post('/api/userPromotion/withdraw/allRecords', queryParams)
        if (res.data.code === 0) {
            tableData.value = res.data.data || []
            total.value = res.data.total || 0
            
            // 更新统计数据
            updateStats()
        } else {
            ElMessage.error(res.data.msg || '获取提现记录失败')
        }
    } catch (error) {
        console.error('获取提现记录失败:', error)
        ElMessage.error('获取提现记录失败')
    } finally {
        loading.value = false
    }
}

// 更新统计数据
const updateStats = () => {
    stats.totalCount = tableData.value.length
    stats.pendingCount = tableData.value.filter(item => item.status === 0).length
    stats.successCount = tableData.value.filter(item => item.status === 1).length
    stats.failedCount = tableData.value.filter(item => item.status === 2).length
}

// 重置搜索
const resetSearch = () => {
    searchQuery.query = ''
    searchQuery.status = ''
    searchQuery.pageNum = 1
    fetchData()
}

// 分页处理
const handleSizeChange = (val) => {
    searchQuery.pageSize = val
    searchQuery.pageNum = 1
    fetchData()
}

const handleCurrentChange = (val) => {
    searchQuery.pageNum = val
    fetchData()
}

// 查看详情
const viewDetails = (row) => {
    currentRecord.value = row
    detailDialogVisible.value = true
}

// 处理提现申请
const processWithdraw = (row, status) => {
    currentRecord.value = row
    processForm.withdrawRecordId = row.id
    processForm.status = status
    processForm.remark = ''
    processDialogVisible.value = true
    detailDialogVisible.value = false
}

// 确认处理
const confirmProcess = async () => {
    processing.value = true
    try {
        const res = await api.post('/api/userPromotion/withdraw/process', processForm)
        if (res.data.code === 0) {
            ElMessage.success('处理成功')
            processDialogVisible.value = false
            fetchData() // 刷新数据
        } else {
            ElMessage.error(res.data.msg || '处理失败')
        }
    } catch (error) {
        console.error('处理失败:', error)
        ElMessage.error('处理失败')
    } finally {
        processing.value = false
    }
}

// 获取状态类型
const getStatusType = (status) => {
    switch (status) {
        case 0: return 'warning'  // 待处理
        case 1: return 'success'  // 成功
        case 2: return 'danger'   // 失败
        default: return 'info'
    }
}

// 获取状态文本
const getStatusText = (status) => {
    switch (status) {
        case 0: return '待处理'
        case 1: return '成功'
        case 2: return '失败'
        default: return '未知'
    }
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
    if (!dateTime) return '-'
    return new Date(dateTime).toLocaleString('zh-CN')
}

// 初始化
onMounted(() => {
    fetchData()
})
</script>

<style scoped>
.pagination-container {
    padding: 20px;
}

.toolbar {
    margin-bottom: 20px;
}

.toolbar-item {
    margin-bottom: 10px;
}

.stats-row {
    margin-bottom: 20px;
}

.stats-card {
    text-align: center;
    cursor: default;
}

.stats-card.pending {
    border-left: 4px solid var(--el-color-warning);
}

.stats-card.success {
    border-left: 4px solid var(--el-color-success);
}

.stats-card.failed {
    border-left: 4px solid var(--el-color-danger);
}

.stats-content {
    padding: 10px 0;
}

.stats-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--el-text-color-primary);
    margin-bottom: 5px;
}

.stats-label {
    font-size: 14px;
    color: var(--el-text-color-regular);
}

.user-token {
    font-family: monospace;
    font-size: 12px;
}

.amount-text {
    font-weight: 600;
    font-size: 14px;
}

.pagination {
    margin-top: 20px;
    display: flex;
    justify-content: center;
}

/* 移动端样式 */
.mobile-card-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.mobile-card {
    margin-bottom: 0;
}

.mobile-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--el-border-color-light);
}

.mobile-card-title {
    font-weight: 600;
    font-size: 14px;
}

.mobile-card-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 12px;
}

.mobile-card-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.mobile-card-item .label {
    font-size: 13px;
    color: var(--el-text-color-regular);
    min-width: 70px;
}

.mobile-card-item .value {
    font-size: 13px;
    font-weight: 500;
    flex: 1;
    text-align: right;
}

.mobile-card-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.detail-content {
    padding: 10px 0;
}

.remark-content {
    word-break: break-all;
    white-space: pre-wrap;
}

.qr-code-section {
    margin-top: 20px;
    text-align: center;
}

.qr-code-section h4 {
    margin-bottom: 10px;
    color: var(--el-text-color-primary);
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

@media (max-width: 768px) {
    .pagination-container {
        padding: 10px;
    }
    
    .toolbar {
        margin-bottom: 15px;
    }
    
    .stats-row {
        margin-bottom: 15px;
    }
}
</style>
